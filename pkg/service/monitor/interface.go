package monitor

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

type ServiceInterface interface {
	// GetMonitorInstances 获取监控实例列表
	GetMonitorInstances(ctx csmContext.CsmContext, clusterID, instanceId string) (*vo.MonitorInstances, error)

	UpdateMonitor(ctx csmContext.CsmContext, instanceId string, update *vo.MonitorInstances) (*vo.MonitorInstances, error)

	UpdateRemoteMonitor(ctx csmContext.CsmContext, instanceId, clusterID, jobId string, update *vo.MonitorInstances) error

	// GetMonitorInstanceDetail 网格实例对应的监控实例详细信息
	GetMonitorInstanceDetail(ctx csmContext.CsmContext, instanceId string) (*vo.MonitorInstanceDetail, error)

	// CreateGatewayMonitor for gateway
	CreateGatewayMonitor(ctx csmContext.CsmContext, csmInstanceID, vpcID string, create *meta.CPromInstance) (*meta.CPromGatewayInfo, error)

	ClusterCPromAgentCheck(ctx csmContext.CsmContext, region, cpromInstanceID, clusterID string) *vo.CPromAgentCheckResult

	// QueryPrometheusMetrics prometheus查询接口
	QueryPrometheusMetrics(ctx csmContext.CsmContext, instanceId, metricType, query, step, start, end string) (interface{}, error)

	// GetCpromInstances 获取指定region下的所有监控实例
	GetCpromInstances(ctx csmContext.CsmContext) ([]meta.CPromInstance, error)

	// CreateInstanceToken 创建实例token
	CreateInstanceToken(ctx csmContext.CsmContext, instanceId string) (string, error)

	// GetInstanceTokenList 获取实例token
	GetInstanceTokenList(ctx csmContext.CsmContext, instanceId string) (*meta.CPromInstanceTokens, error)

	// GetCpromInstanceDetail 获取指定CProm实例的详细信息
	GetCpromInstanceDetail(ctx csmContext.CsmContext, instanceId string) (*meta.CPromItem, error)
}
