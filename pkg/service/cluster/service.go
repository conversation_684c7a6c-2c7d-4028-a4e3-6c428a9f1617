package cluster

import (
	"context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"strings"
	"sync"

	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/jucardi/go-streams/streams"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	instance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	modelMonitor "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/bls"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	instance_service "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances"
	serviceMeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/sliceutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

type Service struct {
	opt *Option

	instanceModel instance.ServiceInterface
	clusterModel  cluster.ServiceInterface

	instanceService instance_service.ServiceInterface
	cceService      cce.ClientInterface
	monitorModel    modelMonitor.ServiceInterface

	blsService bls.ServiceInterface
}

// NewClusterService 新建一个 ClusterService 实例，用于管理集群相关的操作
// 参数 option：包含了数据库连接信息的 Option 指针，可以为 nil
// 返回值 *Service：返回一个 ClusterService 类型的指针
func NewClusterService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt: option,

		instanceModel:   instance.NewInstancesService(instance.NewOption(gormDB)),
		clusterModel:    cluster.NewClusterService(cluster.NewOption(gormDB)),
		monitorModel:    modelMonitor.NewService(modelMonitor.NewOption()),
		instanceService: instance_service.NewInstanceService(instance_service.NewOption(gormDB)),
		cceService:      cce.NewClientService(),
		blsService:      bls.NewBlsService(bls.NewOption(gormDB)),
	}
}

func (s *Service) GetManagedClusters(ctx csmContext.CsmContext, instanceId string, p *meta.Page, lf *meta.ListFilter) (
	[]*meta.ManagedCluster, *meta.PageResult, error) {
	if p == nil {
		p = meta.GetPageParam(ctx, nil)
		p.PageSize = 100
	}
	if lf == nil {
		lf = &meta.ListFilter{
			Filters: map[string][]string{},
		}
	}
	// 将 ListFilter 转换成 where and search
	search := &meta.Cluster{}
	switch strings.ToLower(lf.KeywordType) {
	case "":
		ctx.CsmLogger().Warnf("there is no keyword type %s", lf.KeywordType)
	case strings.ToLower(vo.QueryClusterName):
		if len(lf.Keyword) != 0 {
			search.ClusterName = lf.Keyword
		}
	default:
		ctx.CsmLogger().Errorf("not support keyword type %s", lf.KeywordType)
	}

	// TODO: 暂时只支持单个筛选，升级 gorm 2.0 后支持多选
	where := &meta.Cluster{}
	where.InstanceUUID = instanceId
	for k, v := range lf.Filters {
		switch strings.ToLower(k) {
		case "":
			ctx.CsmLogger().Warnf("there is no filter key %s", k)
		case strings.ToLower(vo.QueryClusterStatus):
			where.ClusterName = v[0]
		case strings.ToLower(vo.QueryClusterRegion):
			where.Region = v[0]
		case strings.ToLower(vo.QueryClusterConnectionState):
			where.ConnectionState = v[0]
		default:
			ctx.CsmLogger().Errorf("not support filter key %s", k)
		}
	}

	clusters, totalCount, err := s.clusterModel.GetManagedClustersByPage(ctx, p, where, search)
	if err != nil {
		// TODO: 后续自定义错误输出，并把原始错误信息包装
		return nil, nil, err
	}

	var wg sync.WaitGroup
	for _, c := range clusters {
		wg.Add(1)
		go func(c *meta.ManagedCluster) {
			defer wg.Done()
			cceCluster, cceClusterErr := s.cceService.GetCCEClusterV2(ctx, c.Region, c.ClusterId)
			if cceClusterErr != nil {
				ctx.CsmLogger().Errorf("Get cce cluster v2 error: %v", cceClusterErr)
				// 根据错误类型设置不同的状态
				if strings.Contains(cceClusterErr.Error(), string(meta.ClusterNotFound)) {
					c.Status = meta.ClusterStatusDeleted
					cs := meta.ClusterNotConnectedState
					c.ConnectionState = &cs
				} else {
					// 其他错误情况，设置为未知状态
					c.Status = meta.ClusterStatusUnknown
					cs := meta.ClusterUnknownConnectedState
					c.ConnectionState = &cs
				}
			} else {
				// 成功获取到集群信息，更新所有字段
				c.ContainerNet = cceCluster.Cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR
				c.Version = string(cceCluster.Cluster.Spec.K8SVersion)
				c.VpcId = cceCluster.Cluster.Spec.VPCID
				c.VpcName = cceCluster.Cluster.Spec.VPCCIDR
				c.Status = meta.ClusterStatus(cceCluster.Cluster.Status.ClusterPhase)

				// 根据集群状态设置连接状态
				switch c.Status {
				case meta.ClusterStatusRunning:
					// 集群运行中，设置为已连接状态
					cs := meta.ClusterConnectedState
					c.ConnectionState = &cs
				case meta.ClusterStatusDeleted, meta.ClusterStatusDeleting:
					// 集群已删除或删除中，设置为未连接状态
					cs := meta.ClusterNotConnectedState
					c.ConnectionState = &cs
				case meta.ClusterStatusCreateFailed, meta.ClusterStatusDeleteFailed:
					// 集群创建或删除失败，设置为未连接状态
					cs := meta.ClusterNotConnectedState
					c.ConnectionState = &cs
				default:
					// 其他状态（pending, provisioning, provisioned等），设置为未知连接状态
					cs := meta.ClusterUnknownConnectedState
					c.ConnectionState = &cs
				}
			}
		}(c)
	}
	// TODO: 确定获取到部分集群的逻辑, 当前为仍输出不报错
	wg.Wait()

	pageResult := &meta.PageResult{
		PageSize:   p.PageSize,
		PageNo:     p.PageNo,
		TotalCount: totalCount,
	}
	return clusters, pageResult, nil
}

// GetCandidateClusters 可纳管集群 = 所有 region cce 集群 - 已纳管集群
func (s *Service) GetCandidateClusters(ctx csmContext.CsmContext, instanceId string, p *meta.Page, lf *meta.ListFilter) (
	[]meta.CandidateCluster, *meta.PageResult, error) {
	if p == nil {
		p = meta.GetPageParam(ctx, nil)
		p.PageSize = 100
	}
	if lf == nil {
		lf = &meta.ListFilter{
			Filters: map[string][]string{},
		}
	}

	cceClustersFiltered := make([]*cce_v2.Cluster, 0)
	cceClusters := make([]*cce_v2.Cluster, 0)
	managedClusterIdRegions := make(map[string]string)

	// 获取网格实例信息
	instance, err := s.instanceModel.GetInstanceByInstanceUUID(ctx, instanceId)
	if err != nil {
		return nil, nil, err
	}

	// api 获取 cce 集群
	var wg sync.WaitGroup
	var lock sync.Mutex

	// allRegionShort := reg.GetAllRegionShort()
	// 目前暂定只获取实例所在的 region 对应的所有 cce 集群
	// TODO 后续可以考虑支持多地域跨集群
	allRegionShort := []string{instance.Region}
	for _, r := range allRegionShort {
		wg.Add(1)
		go func(region string) {
			defer wg.Done()
			csp, err := s.cceService.GetCCEClustersV2(ctx, region, lf.KeywordType, lf.Keyword)
			if err != nil {
				ctx.CsmLogger().Errorf("Get cce cluster error. %v", err)
			} else {
				lock.Lock()
				for _, c := range csp.ClusterPage.ClusterList {
					managedClusterIdRegions[c.Spec.ClusterID] = region
				}
				cceClusters = append(cceClusters, csp.ClusterPage.ClusterList...)
				lock.Unlock()
			}
		}(r)
	}
	wg.Wait()
	// TODO: 确定获取到部分集群的逻辑, 当前为仍输出不报错

	managedClusterIds := make(map[string]struct{})
	// 若网格类型为独立网格且为命名空间级别，不需要过滤已纳管集群，仅需过滤主集群（适用于内部 EKS 用户）
	if len(instance.InstanceManageScope) > 0 && instance.InstanceType == string(version.StandaloneVersionType) &&
		instance.InstanceManageScope == string(meta.InstanceManageNamespaceScope) {
		// 获取主集群
		istiodCluster, _, err := s.instanceModel.GetInstanceIstiodCluster(ctx, instanceId)
		if err != nil {
			return nil, nil, err
		}
		managedClusterIds[istiodCluster.ClusterUUID] = struct{}{}
	} else {
		// 获取已纳管集群
		managedClusters, err := s.clusterModel.GetManagedClustersByUser(ctx)
		if err != nil {
			return nil, nil, err
		}

		for _, c := range managedClusters {
			managedClusterIds[c.ClusterId] = struct{}{}
		}
	}

	for _, cc := range cceClusters {
		if _, ok := managedClusterIds[cc.Spec.ClusterID]; !ok {
			cceClustersFiltered = append(cceClustersFiltered, cc)
		}
	}

	// 转换模型
	clusters := make([]meta.CandidateCluster, 0, len(cceClustersFiltered))
	for _, filtered := range cceClustersFiltered {
		filteredCluster := meta.CandidateCluster{
			ClusterId:      filtered.Spec.ClusterID,
			ClusterName:    filtered.Spec.ClusterName,
			Status:         meta.ClusterStatus(filtered.Status.ClusterPhase),
			Version:        string(filtered.Spec.K8SVersion),
			MasterMode:     meta.ClusterMasterMode(filtered.Spec.MasterConfig.MasterType),
			NetworkSegment: filtered.Spec.ContainerNetworkConfig.ClusterPodCIDR, // TODO: 待确认
			Region:         managedClusterIdRegions[filtered.Spec.ClusterID],
			Available:      true,
			VpcInfo: meta.VpcInfo{
				VpcId:   filtered.Spec.VPCID,
				VpcName: filtered.Spec.VPCCIDR, // TODO: 待确认
			},
		}
		clusters = append(clusters, filteredCluster)
	}

	// 内存中实现筛选和模糊查询
	keywordType := lf.KeywordType
	keyword := lf.Keyword

	cs := streams.
		FromArray(clusters).
		Filter(func(v interface{}) bool {
			c := v.(meta.CandidateCluster)
			statusFilters := lf.Filters[vo.QueryClusterStatus]
			return contains(statusFilters, string(c.Status))
		}).
		Filter(func(v interface{}) bool {
			c := v.(meta.CandidateCluster)
			regionFilters := lf.Filters[vo.QueryClusterRegion]
			return contains(regionFilters, string(c.Region))
		}).
		Filter(func(v interface{}) bool {
			c := v.(meta.CandidateCluster)
			modeFilters := lf.Filters[vo.QueryClusterMasterMode]
			return contains(modeFilters, string(c.MasterMode))
		}).
		Filter(func(v interface{}) bool {
			c := v.(meta.CandidateCluster)
			if strings.EqualFold(keywordType, vo.QueryClusterName) {
				return strings.Contains(c.ClusterName, keyword)
			}
			return true
		}).
		Filter(func(v interface{}) bool {
			c := v.(meta.CandidateCluster)
			if strings.EqualFold(keywordType, vo.QueryClusterVpcName) {
				return strings.Contains(c.VpcName, keyword)
			}
			return true
		}).
		ToArray().([]meta.CandidateCluster)

	pageResult := &meta.PageResult{
		PageSize:   p.PageSize,
		PageNo:     p.PageNo,
		TotalCount: int64(len(cs)),
	}

	start := (p.PageNo - 1) * p.PageSize
	end := util.MinInt64(p.PageNo*p.PageSize, int64(len(cs)))
	if start >= end {
		return []meta.CandidateCluster{}, pageResult, nil
	}
	clustersWithSlice := cs[start:end]
	return clustersWithSlice, pageResult, nil
}

func contains(s []string, str string) bool {
	// 规定默认行为，如果切片为空，则代表全部
	if len(s) == 0 {
		return true
	}

	if len(strings.TrimSpace(str)) == 0 {
		return false
	}
	for _, v := range s {
		if strings.EqualFold(strings.TrimSpace(v), strings.TrimSpace(str)) {
			return true
		}
	}
	return false
}

// AddClusters 添加远程集群，如果是托管版本则创建用户集群，否则创建远程集群
// ctx: csmContext.CsmContext 上下文信息
// instanceId: string 实例id
// clusters: []meta.CandidateCluster 需要添加的远程集群列表，包含region和clusterId两个字段
// 返回值: error 错误信息，如果成功则为nil
func (s *Service) AddClusters(ctx csmContext.CsmContext, instanceId string, clusters []meta.CandidateCluster) error {
	// TODO: 改成批量
	// TODO: 添加安装远程集群操作
	// TODO: 添加事务操作
	accountId, _ := iam.GetAccountId(ctx)
	instance, err := s.instanceModel.GetInstanceByInstanceUUID(ctx, instanceId)
	if err != nil {
		return err
	}

	instanceCluster, _, err := s.instanceModel.GetInstanceIstiodCluster(ctx, instanceId)
	if err != nil {
		return err
	}

	client, clientErr := s.cceService.NewClient(ctx, instance.Region, instanceCluster.ClusterUUID, meta.MeshType(instance.InstanceType))
	if clientErr != nil {
		return csmErr.AddCluster()
	}
	svcName := constants.EastWestGateway
	if instance.InstanceType == string(version.HostingVersionType) {
		svcName = constants.IstiodServiceName
	}
	var blbId, blbStatus string
	svc, svcErr := client.Kube().CoreV1().Services(instanceCluster.IstioInstallNamespace).Get(context.TODO(), svcName, metav1.GetOptions{})
	if svcErr != nil {
		return csmErr.AddCluster()
	}
	blbId = svc.Annotations[constants.BLBId]
	blbStatus = constants.BLBRunning
	if blbId == "" {
		blbStatus = constants.BLBPending
	}
	if blbStatus != constants.BLBRunning {
		return csmErr.AddCluster()
	}

	istioInstallNamespace := constants.IstioNamespace
	if strings.EqualFold(instance.InstanceManageScope, string(meta.InstanceManageNamespaceScope)) {
		istioInstallNamespace = util.GetNamespaceWithCsmInstanceId(istioInstallNamespace, instance.InstanceUUID, "-")
	}

	// 托管集群下用户集群安装命名空间使用实例所在的命名空间
	if instance.InstanceType == string(version.HostingVersionType) {
		istioInstallNamespace = instance.IstioInstallNamespace
	}
	// 托管网格执行添加远端集群逻辑，需要判断configCluster是external还是remote。
	// 如果是放在remote集群，需要判断是否为第一个remote集群
	isConfigCluster := false
	if instance.ConfigCluster == constants.ConfigClusterREMOTE {
		remoteClusterList, remoteErr := s.clusterModel.GetAllRemoteClusterByInstanceUUID(ctx, instanceId)
		if remoteErr != nil {
			return remoteErr
		}
		if remoteClusterList == nil || len(*remoteClusterList) == 0 {
			isConfigCluster = true
		}
	}

	ctx.CsmLogger().Infof("AddClusters add clusters with %v", clusters)
	for _, c := range clusters {
		region := c.Region
		clusterId := c.ClusterId
		ctx.CsmLogger().Infof("GetCCECluster region=%s, clusterId=%s", region, clusterId)
		cceCluster, err := s.cceService.GetCCECluster(ctx, region, clusterId)
		if err != nil {
			return err
		}

		cluster := &meta.Cluster{
			InstanceUUID:          instanceId,
			ClusterUUID:           clusterId,
			ClusterName:           cceCluster.ClusterName,
			ClusterType:           string(meta.ClusterTypeRemote),
			Region:                region,
			ConnectionState:       "",
			AccountId:             accountId,
			IstioInstallNamespace: istioInstallNamespace,
			Deleted:               csm.Int(0),
			MonitorInstanceId:     "",
			MonitorRegion:         "",
			MonitorJobIds:         "",
		}
		if instance.InstanceType == string(version.HostingVersionType) {
			ctx.CsmLogger().Infof("add user remote cluster on hosting mesh instance")
			paasType := meta.PaaSTypeCCE
			if sliceutil.StringContains(s.opt.EksAccountIds, accountId) {
				paasType = meta.PaaSTypeEKS
			}
			createRemoteUserMeshCluster := serviceMeta.ToCreateRemoteUserMeshCluster(paasType, instance, cluster)
			createRemoteUserMeshCluster.InstallationClusterName = instanceCluster.ClusterName
			createRemoteUserMeshCluster.InstallationClusterId = instanceCluster.ClusterUUID
			createRemoteUserMeshCluster.AccountId = accountId
			// 第一个remote集群，执行configCluster集群安装思路
			if isConfigCluster {
				cluster.ClusterType = string(meta.ClusterTypeConfig)
				createRemoteUserMeshCluster.ClusterModel.ClusterType = string(meta.ClusterTypeConfig)
				clusterID := cluster.Region + "-" + cluster.ClusterUUID
				go func(ctx csmContext.CsmContext, client kube.Client, clusterID string) {
					addErr := s.instanceService.AddRemoteConfigMeshCluster(ctx, createRemoteUserMeshCluster)
					if addErr != nil {
						ctx.CsmLogger().Errorf("AddRemoteConfigMeshCluster failed, err is %s", addErr.Error())
					}

					// 创建完config集群后，需要去掉"ISTIO_CRD_NAMESPACE_NAME"环境变量值，让istiod监听config集群的所有命名空间。
					deployName := constants.IstiodDeploymentName
					istioDeploy, deployErr := client.Kube().AppsV1().Deployments(instanceCluster.IstioInstallNamespace).Get(
						context.TODO(), deployName, metav1.GetOptions{})
					if deployErr != nil {
						ctx.CsmLogger().Errorf("upgate ISTIO_CRD_NAMESPACE_NAME failed, err is %s", deployErr.Error())
					}
					if istioDeploy != nil {
						for index, container := range istioDeploy.Spec.Template.Spec.Containers {
							if container.Name != constants.IstioContainerName {
								continue
							}
							for envIndex, env := range container.Env {
								if env.Name == constants.IstioCRDNAMESPACENAME {
									istioDeploy.Spec.Template.Spec.Containers[index].Env[envIndex].Value = ""
								}
								if env.Name == constants.IstioENVClusterID {
									istioDeploy.Spec.Template.Spec.Containers[index].Env[envIndex].Value = clusterID
								}
							}
						}
					}
					// 更新istio的deploy
					_, updateErr := client.Kube().AppsV1().Deployments(instanceCluster.IstioInstallNamespace).Update(
						context.TODO(), istioDeploy, metav1.UpdateOptions{})
					if updateErr != nil {
						ctx.CsmLogger().Errorf("upgate istiod deployment failed, err is %s", updateErr.Error())
					}
				}(ctx, client, clusterID)
			} else {
				go func(ctx csmContext.CsmContext) {
					addErr := s.instanceService.AddRemoteUserMeshCluster(ctx, createRemoteUserMeshCluster)
					if addErr != nil {
						ctx.CsmLogger().Errorf("AddRemoteUserMeshCluster failed, err is %s", addErr.Error())
					}
				}(ctx)
			}
		} else {
			if sliceutil.StringContains(s.opt.EksAccountIds, accountId) {
				ctx.CsmLogger().Infof("install istio cluster on eks")
				go s.instanceService.InstallRemoteMeshCluster(ctx, instance, instanceCluster, cluster, meta.PaaSTypeEKS)
			} else {
				ctx.CsmLogger().Infof("install istio remote cluster on cce")
				go s.instanceService.InstallRemoteMeshCluster(ctx, instance, instanceCluster, cluster, meta.PaaSTypeCCE)
			}
			// 独立网格，如果开启了Prometheus监控，需要添加数据面采集任务
			if *instance.MonitorEnabled {
				cluster.MonitorInstanceId = instanceCluster.MonitorInstanceId
				cluster.MonitorRegion = instanceCluster.MonitorRegion
				jobIds := make([]string, 0)
				agent, err := s.monitorModel.GetCPromAgent(ctx, cluster.MonitorInstanceId, cluster.MonitorRegion, cluster.ClusterUUID)
				if err != nil {
					ctx.CsmLogger().Warnf("get cprom agent failed. %v", err)
					return err
				}
				envoyJobId, err := s.monitorModel.CreateEnvoyScrapeJob(ctx, cluster.MonitorRegion, cluster.MonitorInstanceId, agent.AgentID)
				if err != nil {
					ctx.CsmLogger().Errorf("create envoy scrape job failed. %v", err)
					return err
				}
				jobIds = append(jobIds, envoyJobId)
				cluster.MonitorJobIds = strings.Join(jobIds, ",")
			}
		}
		s.clusterModel.NewCluster(ctx, cluster)
		err = s.blsService.BlsAddCluster(ctx, instanceId, clusterId, region)
		if err != nil {
			ctx.CsmLogger().Errorf("add cluster to bls error %v", err)
		}
		isConfigCluster = false
	}
	// TODO: 是否返回成功 id
	return nil
}

// RemoveClusters 删除集群，包括从数据库中删除、从负载均衡服务中移除、从安装服务中卸载。
// 如果实例类型为托管版本，则会在主集群上安装一个用户集群。
// 参数：
//   - ctx csmContext.CsmContext: 上下文信息，包括请求ID和日志记录器等。
//   - instanceId string: 实例ID。
//   - clusters []meta.ManagedCluster: 需要删除的集群列表，包括集群ID和区域。
//
// 返回值：
//   - error: 如果发生错误，返回对应的错误；否则返回nil。
func (s *Service) RemoveClusters(ctx csmContext.CsmContext, instanceId string, clusters []meta.ManagedCluster) error {
	accountId, _ := iam.GetAccountId(ctx)
	instance, err := s.instanceModel.GetInstanceByInstanceUUID(ctx, instanceId)
	if err != nil {
		return err
	}

	instanceCluster, _, err := s.instanceModel.GetInstanceIstiodCluster(ctx, instanceId)
	if err != nil {
		return err
	}

	// TODO: 改成批量
	for _, c := range clusters {
		region := c.Region
		clusterId := c.ClusterId
		cluster, err := s.clusterModel.GetClusterByIdAndRegion(ctx, instanceId, clusterId, region)
		if err != nil {
			return err
		}
		if instance.InstanceType == string(version.HostingVersionType) {
			ctx.CsmLogger().Infof("remove user remote cluster on hosting mesh instance")
			paasType := meta.PaaSTypeCCE
			if sliceutil.StringContains(s.opt.EksAccountIds, accountId) {
				paasType = meta.PaaSTypeEKS
			}
			deleteRemoteUserMeshCluster := serviceMeta.ToDeleteRemoteUserMeshCluster(paasType, instance, cluster)
			deleteRemoteUserMeshCluster.InstallationClusterName = instanceCluster.ClusterName
			deleteRemoteUserMeshCluster.InstallationClusterId = instanceCluster.ClusterUUID
			go s.instanceService.RemoveRemoteUserMeshCluster(ctx, deleteRemoteUserMeshCluster)
		} else {
			ctx.CsmLogger().Infof("start uninstall remote user cluster")
			go s.instanceService.UnInstallMeshCluster(ctx, instance, cluster)
		}
		err = s.clusterModel.DeleteCluster(ctx, instanceId, clusterId, region)
		if err != nil {
			ctx.CsmLogger().Errorf("remove cluster from database error %v", err)
		}
		ctx.CsmLogger().Infof("remove data from database success")
		err = s.blsService.BlsRemoveCluster(ctx, instanceId, clusterId, region)
		if err != nil {
			ctx.CsmLogger().Errorf("remove cluster from bls error %v", err)
		}

		// 兼容托管网格控制面监控
		if *instance.MonitorEnabled && instance.InstanceType == string(version.HostingVersionType) {
			agent, err := s.monitorModel.GetCPromAgent(ctx, instanceCluster.MonitorInstanceId, instanceCluster.MonitorRegion, cluster.ClusterUUID)
			if err != nil {
				ctx.CsmLogger().Warnf("RemoveClusters GetCPromAgent error %v", err)
				continue
			}
			// 如果当前待删除集群 是部署了托管网格控制面采集任务的集群，则删除集群时，需要清理采集任务。
			// 控制面监控有检测采集任务和agent是否存活的逻辑，这里删除采集任务后的处理逻辑，用户可以在Prometheus监控界面上看到
			if instanceCluster.MonitorAgentID != "" && agent.AgentID == instanceCluster.MonitorAgentID {
				err = s.monitorModel.DeleteScrapeJob(ctx, c.Region, instanceCluster.MonitorJobIds,
					instanceCluster.MonitorInstanceId, instanceCluster.MonitorAgentID)
				if err != nil {
					ctx.CsmLogger().Warnf("DeleteServiceMeshInstance DeleteScrapeJob error %v", err)
				}
			}
			continue
		}

		// 清理监控采集 job
		if len(cluster.MonitorJobIds) == 0 {
			continue
		}
		if len(cluster.MonitorInstanceId) == 0 || len(cluster.MonitorRegion) == 0 {
			continue
		}
		if len(cluster.MonitorInstanceId) != 0 && strings.EqualFold(c.Region, cluster.MonitorRegion) {
			agent, err := s.monitorModel.GetCPromAgent(ctx, cluster.MonitorInstanceId, c.Region, cluster.ClusterUUID)
			if err != nil {
				ctx.CsmLogger().Warnf("RemoveClusters GetCPromAgent error %v", err)
				continue
			}

			for _, jobId := range strings.Split(cluster.MonitorJobIds, ",") {
				err = s.monitorModel.DeleteScrapeJob(ctx, c.Region, jobId, cluster.MonitorInstanceId, agent.AgentID)
				if err != nil {
					ctx.CsmLogger().Warnf("RemoveClusters DeleteScrapeJob error %v", err)
					continue
				}
			}
		}
	}

	return nil
}

// ExistRemoteClusters 只校验remote类型集群，托管网格的config集群在删除托管网格实例的时候删除
func (s *Service) ExistRemoteClusters(ctx csmContext.CsmContext, instancesId string) (bool, error) {
	clusters, err := s.clusterModel.GetAllClusterByInstanceUUID(ctx, instancesId)
	if err != nil {
		return false, err
	}
	remoteCount := 0
	for _, value := range *clusters {
		if value.ClusterType == string(meta.ClusterTypeRemote) {
			remoteCount++
		}
	}
	ctx.CsmLogger().Infof("the mesh instance id %v has %v remote clusters", instancesId, remoteCount)
	return remoteCount > 0, nil
}

// IsRemoteConfigClusters 当前cluster是否为config集群
func (s *Service) IsRemoteConfigClusters(ctx csmContext.CsmContext, instancesID, clusterID, region string) (bool, error) {
	clusterInfo, err := s.clusterModel.GetClusterByIdAndRegion(ctx, instancesID, clusterID, region)
	if err != nil {
		return false, err
	}
	return clusterInfo != nil && clusterInfo.ClusterType == string(meta.ClusterTypeConfig), nil
}
