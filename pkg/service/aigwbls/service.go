package aigwbls

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"path"
	"text/template"

	"github.com/pkg/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/aigateway"
	aigwblsModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/aigwbls"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/bls"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blsv3"
	metaModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"
)

// Service AIGW BLS服务实现
type Service struct {
	opt              *Option
	aigatewayModel   aigateway.ServiceInterface
	aigwblsModel     aigwblsModel.ServiceInterface
	blsv3Service     blsv3.ServiceInterface
	blsService       bls.ServiceInterface
	InstancesService instances.ServiceInterface
	cceService       cce.ClientInterface
}

// NewService 创建AIGW BLS服务实例
func NewService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:              option,
		aigatewayModel:   aigateway.NewAIGatewayService(aigateway.NewOption(gormDB)),
		aigwblsModel:     aigwblsModel.NewAIGatewayBlsService(aigwblsModel.NewOption(gormDB)),
		blsv3Service:     blsv3.NewBlsService(blsv3.NewOption(gormDB)),
		blsService:       bls.NewService(bls.NewOption()),
		InstancesService: instances.NewInstanceService(instances.NewOption(gormDB)),
		cceService:       cce.NewClientService(),
	}
}

// AIGatewayBls 获取AIGW实例的BLS采集任务
func (s *Service) AIGatewayBls(ctx csmContext.CsmContext, region, instanceUUID string) (*meta.BlsListResult, error) {
	ctx.CsmLogger().Infof("AIGW Bls start to get bls, instanceUUID: %s", instanceUUID)

	// 检查AIGW实例是否存在
	gatewayInfo, err := s.aigatewayModel.GetAIGatewayInfo(ctx, instanceUUID, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("AIGW Bls get gateway info error: %v", err)
		return nil, err
	}
	if gatewayInfo == nil || *gatewayInfo == nil || !(*gatewayInfo).BlsEnabled {
		ctx.CsmLogger().Info("AIGW Bls is close")
		return &meta.BlsListResult{
			Status: blsv3.Close,
		}, nil
	}
	hostingClient, err := s.cceService.NewClient(ctx, region, (*gatewayInfo).HostedClusterID, metaModel.HostingMeshType)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create cluster client")
	}
	namespace := "istio-system-" + instanceUUID
	// 获取 taskID
	groupVersionResource := schema.GroupVersionResource{
		Group:    "cce.baidubce.com",
		Version:  "v1",
		Resource: "logconfigs",
	}
	resource, err := hostingClient.Dynamic().Resource(groupVersionResource).Namespace(
		namespace).Get(context.TODO(), "task-"+instanceUUID, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("AIGW Bls get bls tasks error: %v", err)
		return &meta.BlsListResult{
			Status: blsv3.Abnormal,
		}, err
	}
	if resource == nil {
		ctx.CsmLogger().Info("AIGW Bls no bls tasks found, status: Close")
		return &meta.BlsListResult{
			Status: blsv3.Close,
		}, nil
	}
	logStoreName, _, _ := unstructured.NestedString(resource.Object, "spec", "dstConfig", "logStore")
	err = s.blsv3Service.WithLogOperation(ctx, region)
	if err != nil {
		ctx.CsmLogger().Errorf("BlsList service init Log Operation error: %v", err)
		return &meta.BlsListResult{
			Status: blsv3.Abnormal,
		}, fmt.Errorf("LogStoreList service init Log Operation error: %w", err)
	}
	logDetails, err := s.blsv3Service.GetBlsLogs(ctx)
	if err != nil || logDetails == nil {
		ctx.CsmLogger().Errorf("BlsList service get bls logs error: %v", err)
		return &meta.BlsListResult{
			Status: blsv3.Abnormal,
		}, fmt.Errorf("LogStoreList service get bls logs error: %w", err)
	}

	//获取BLS日志集的详细信息
	for _, item := range logDetails.Result {
		if item.LogStoreName == logStoreName {
			res := &meta.BlsListResult{
				Status: blsv3.Open,
				Result: []meta.BlsDetail{
					{
						Name:       item.LogStoreName,
						Retention:  item.Retention,
						CreateTime: string(item.CreationDateTime),
						UpdateTime: string(item.LastModifiedTime),
					},
				},
			}
			return res, nil
		}
	}

	ctx.CsmLogger().Errorf("AIGW BlsList log store not found in BLS")
	return &meta.BlsListResult{
		Status: blsv3.Abnormal,
	}, nil
}

// AIGatewayBlsClose 关闭AIGW实例的BLS日志采集任务
func (s *Service) AIGatewayBlsClose(ctx csmContext.CsmContext, region, instanceUUID string) (bool, error) {
	ctx.CsmLogger().Infof("AIGW BlsClose start, instanceUUID: %s", instanceUUID)

	// 检查AIGW实例是否存在
	gatewayInfo, err := s.aigatewayModel.GetAIGatewayInfo(ctx, instanceUUID, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("AIGW BlsClose get gateway info error: %v", err)
		return false, err
	}
	if gatewayInfo == nil || *gatewayInfo == nil {
		ctx.CsmLogger().Info("AIGW BlsClose gateway not found")
		return false, nil
	}
	hostingClient, err := s.cceService.NewClient(ctx, region, (*gatewayInfo).HostedClusterID, metaModel.HostingMeshType)
	if err != nil {
		return false, errors.Wrap(err, "failed to create cluster client")
	}

	var errMsg string
	groupVersionResource := schema.GroupVersionResource{
		Group:    "cce.baidubce.com",
		Version:  "v1",
		Resource: "logconfigs",
	}
	namespace := "istio-system-" + instanceUUID
	err = hostingClient.Dynamic().Resource(groupVersionResource).Namespace(
		namespace).Delete(context.TODO(), "task-"+instanceUUID, metav1.DeleteOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("AIGW BlsClose close task error: %v", err)
		errMsg = err.Error()
	}

	// 更新AIGW实例的BLS状态为关闭
	updatedGateway := gatewayInfo
	(*updatedGateway).BlsEnabled = false
	if err := s.aigatewayModel.UpdateAIGateway(ctx, gatewayInfo, updatedGateway); err != nil {
		ctx.CsmLogger().Errorf("AIGW BlsClose update gateway bls status error: %v", err)
		return false, err
	}

	if errMsg == "" {
		ctx.CsmLogger().Infof("AIGW BlsClose delete task: %s", instanceUUID)
		return true, nil
	}

	return false, nil
}

// AIGatewayBlsOpen 开启AIGW实例的BLS日志采集任务
func (s *Service) AIGatewayBlsOpen(ctx csmContext.CsmContext, region, instanceUUID, logStoreName string) (bool, error) {
	ctx.CsmLogger().Infof("AIGW BlsOpen start, instanceUUID: %s, logStoreName: %s", instanceUUID, logStoreName)

	// 检查AIGW实例是否存在
	gatewayInfo, err := s.aigatewayModel.GetAIGatewayInfo(ctx, instanceUUID, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("AIGW BlsOpen get gateway info error: %v", err)
		return false, err
	}
	if gatewayInfo == nil || *gatewayInfo == nil {
		ctx.CsmLogger().Error("AIGW BlsOpen gateway not found")
		return false, fmt.Errorf("gateway not found")
	}

	hostedClusterId := (*gatewayInfo).HostedClusterID
	hostingClient, err := s.cceService.NewClient(ctx, region, hostedClusterId, metaModel.HostingMeshType)
	if err != nil {
		return false, errors.Wrap(err, "failed to create cluster client")
	}

	// 先清空之前的任务
	_, err = s.AIGatewayBlsClose(ctx, region, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("AIGW BlsOpen close previous tasks error: %v", err)
		return false, err
	}

	// 获取用户账户ID
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("AIGW BlsOpen get account id error: %v", err)
		return false, err
	}

	namespace := "istio-system-" + instanceUUID
	name := "task-" + instanceUUID
	data := metaModel.BlsTemplateData{
		InstanceUUID: instanceUUID,
		ClusterID:    hostedClusterId,
		LogStore:     logStoreName,
		Namespace:    namespace,
		AccountID:    accountId,
	}
	// 读取模板文件
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("获取当前目录失败: %v", err)
		return false, errors.Wrap(err, "failed to get current directory")
	}

	// 使用 BLS 模板路径
	blsTemplatePath := path.Join(pwd, constants.GetBlsTemplatePath())
	ctx.CsmLogger().Infof("尝试读取 BLS 模板文件: %s", blsTemplatePath)

	templateBytes, err := os.ReadFile(blsTemplatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("读取 BLS 模板文件失败: %v", err)
		return false, errors.Wrap(err, "failed to read BLS template file")
	}

	// 渲染模板
	tmpl, err := template.New(name).Parse(string(templateBytes))
	if err != nil {
		ctx.CsmLogger().Errorf("解析 BLS 模板失败: %v", err)
		return false, errors.Wrap(err, "failed to parse BLS template")
	}

	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, data); err != nil {
		ctx.CsmLogger().Errorf("渲染 BLS 模板失败: %v", err)
		return false, errors.Wrap(err, "failed to render BLS template")
	}

	// 应用渲染后的YAML
	blsObjects, err := object.ManifestK8sObject(ctx, rendered.String())
	if err != nil {
		ctx.CsmLogger().Errorf("解析 BLS 模板manifest失败: %v", err)
		return false, errors.Wrap(err, "failed to parse BLS template manifest")
	}

	// 创建资源
	err = kube.CreateResources(ctx, hostingClient, blsObjects)
	if err != nil {
		ctx.CsmLogger().Errorf("创建 BLS 传输任务失败: %v", err)
		return false, errors.Wrap(err, "failed to create BLS resources")
	}

	// 更新AIGW实例的BLS状态为打开
	updatedGateway := gatewayInfo
	(*updatedGateway).BlsEnabled = true
	if err := s.aigatewayModel.UpdateAIGateway(ctx, gatewayInfo, updatedGateway); err != nil {
		ctx.CsmLogger().Errorf("AIGW BlsClose update gateway bls status error: %v", err)
		return false, err
	}

	ctx.CsmLogger().Infof("AIGW BlsOpen success, instanceUUID: %s", instanceUUID)
	return true, nil
}
