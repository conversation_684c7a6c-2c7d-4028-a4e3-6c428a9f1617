package aigwbls

import (
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

// ServiceInterface AIGW BLS服务接口
type ServiceInterface interface {
	AIGatewayBls(ctx csmContext.CsmContext, region, instanceUUID string) (*meta.BlsListResult, error)
	AIGatewayBlsClose(ctx csmContext.CsmContext, region, instanceUUID string) (bool, error)
	AIGatewayBlsOpen(ctx csmContext.CsmContext, region, instanceUUID, logStoreName string) (bool, error)
}
