package aigateway

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/aigateway/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Service struct {
	opt *Option
	dao model.AIGatewayDaoInterface
}

func NewAIGatewayService(option *Option) *Service {
	return &Service{
		opt: option,
		dao: dao.NewAIGatewayDao(option.DB),
	}
}

// WithTx 网关实例 tx
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	nOpt := *s.opt
	nOpt.DB = tx
	return NewAIGatewayService(&nOpt)
}

// NewAIGateway 创建网关实例
func (s *Service) NewAIGateway(ctx csmContext.CsmContext, gateway **meta.AIGatewayInstanceModel) error {
	return s.dao.Save(ctx, *gateway)
}

// UpdateAIGateway 更新网关实例
func (s *Service) UpdateAIGateway(ctx csmContext.CsmContext, dstGateway, updateGateway **meta.AIGatewayInstanceModel) error {
	return s.dao.Update(ctx, *dstGateway, *updateGateway)
}

// DeleteAIGateway 删除网关实例
func (s *Service) DeleteAIGateway(ctx csmContext.CsmContext, instanceUUID, gatewayUUID string) error {
	where := &meta.AIGatewayInstanceModel{
		InstanceUUID: instanceUUID,
		GatewayUUID:  gatewayUUID,
	}
	return s.dao.BatchDelete(ctx, where)
}

// GetAIGatewayInfo GetGatewayInfo 根据instanceUUID和gatewayUUID获取网关信息
func (s *Service) GetAIGatewayInfo(ctx csmContext.CsmContext, instanceUUID, gatewayUUID string) (**meta.AIGatewayInstanceModel, error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}
	where := &meta.AIGatewayInstanceModel{
		Deleted:      csm.Int(0),
		AccountID:    accountId,
		InstanceUUID: instanceUUID,
		GatewayUUID:  gatewayUUID,
	}
	gatewayInfo, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}
	result := gatewayInfo.(*meta.AIGatewayInstanceModel)
	return &result, nil
}

// GetAIGatewayList 获取网关列表
func (s *Service) GetAIGatewayList(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (*[]*meta.AIGatewayInstanceModel, error) {
	search := &meta.AIGatewayInstanceModel{}
	if mrp.Keyword != "" {
		switch mrp.KeywordType {
		case GatewayName:
			search.GatewayName = mrp.Keyword
		case GatewayUUID:
			search.GatewayUUID = mrp.Keyword
		}
	}
	where := &meta.AIGatewayInstanceModel{
		Deleted:      csm.Int(0),
		AccountID:    mrp.AccountID,
		InstanceUUID: mrp.InstanceUUID,
	}

	// 如果指定了排除的 srcProduct，则添加到查询条件中
	not := &meta.AIGatewayInstanceModel{}
	if mrp.ExcludeSrcProduct != "" {
		not.SrcProduct = mrp.ExcludeSrcProduct
	}

	// 使用ListAll方法以支持not条件过滤
	gatewayList, err := s.dao.ListAll(ctx, search, where, not)
	if err != nil {
		return nil, err
	}

	result := gatewayList.(*[]meta.AIGatewayInstanceModel)
	// 转换结果格式
	var convertedList []*meta.AIGatewayInstanceModel
	for i := range *result {
		convertedList = append(convertedList, &(*result)[i])
	}
	return &convertedList, nil
}
