package meta

// MetricsStatusResult 监控状态结果
type MetricsStatusResult struct {
	Enabled         bool   `json:"enabled"`                   // 是否开启监控
	CpromInstanceId string `json:"cpromInstanceId,omitempty"` // CProm实例ID（仅在开启时返回）
}

// MetricsStatusResponse 监控状态响应
type MetricsStatusResponse struct {
	Success bool                `json:"success"`
	Result  MetricsStatusResult `json:"result"`
	Status  int                 `json:"status"`
}

// EnableMetricsResult 开启监控结果
type EnableMetricsResult struct {
	InstanceId      string `json:"instanceId"`      // AI网关实例ID
	CpromInstanceId string `json:"cpromInstanceId"` // CProm实例ID
	Token           string `json:"token"`           // CProm访问token
	Message         string `json:"message"`         // 操作消息
}

// EnableMetricsResponse 开启监控响应
type EnableMetricsResponse struct {
	Success bool                `json:"success"`
	Result  EnableMetricsResult `json:"result"`
	Status  int                 `json:"status"`
}

// DisableMetricsResult 关闭监控结果
type DisableMetricsResult struct {
	InstanceId string `json:"instanceId"` // AI网关实例ID
	Message    string `json:"message"`    // 操作消息
}

// DisableMetricsResponse 关闭监控响应
type DisableMetricsResponse struct {
	Success bool                 `json:"success"`
	Result  DisableMetricsResult `json:"result"`
	Status  int                  `json:"status"`
}

// ValidateTokenResult 验证token结果
type ValidateTokenResult struct {
	Valid       bool   `json:"valid"`       // token是否有效
	CurrentToken string `json:"currentToken,omitempty"` // 当前使用的token值（仅在valid为true时返回）
}

// ValidateTokenResponse 验证token响应
type ValidateTokenResponse struct {
	Success bool                `json:"success"`
	Result  ValidateTokenResult `json:"result"`
	Status  int                 `json:"status"`
}

// CpromInstancesResponse CProm实例列表响应
type CpromInstancesResponse struct {
	Success bool                    `json:"success"`
	Result  []CpromInstanceListItem `json:"result"`
	Status  int                     `json:"status"`
}

// CpromInstanceListItem CProm实例列表项（完整详情）
type CpromInstanceListItem struct {
	InstanceId          string                      `json:"instanceId"`
	InstanceName        string                      `json:"instanceName"`
	Region              string                      `json:"region"`
	Metadata            *CpromInstanceMetadata      `json:"metadata"`
	Spec                *CpromInstanceSpec          `json:"spec"`
	Status              *CpromInstanceStatus        `json:"status"`
	MonitorGrafanaId    string                      `json:"monitorGrafanaId"`
	MonitorGrafanaName  string                      `json:"monitorGrafanaName"`
}

// CpromInstanceMetadata CProm实例元数据
type CpromInstanceMetadata struct {
	Name              string            `json:"name"`
	Labels            map[string]string `json:"labels"`
	Annotations       interface{}       `json:"annotations"`
	CreationTimestamp string            `json:"creationTimestamp"`
}

// CpromInstanceSpec CProm实例规格配置
type CpromInstanceSpec struct {
	InstanceID      string                     `json:"instanceID"`
	InstanceName    string                     `json:"instanceName"`
	Region          string                     `json:"region"`
	VmClusterConfig *CpromVmClusterConfig      `json:"vmClusterConfig"`
	GrafanaConfig   *CpromGrafanaConfig        `json:"grafanaConfig"`
	Tags            interface{}                `json:"tags"`
}

// CpromVmClusterConfig VM集群配置
type CpromVmClusterConfig struct {
	RetentionPeriod string `json:"retentionPeriod"`
}

// CpromGrafanaConfig Grafana配置
type CpromGrafanaConfig struct {
	Enable        bool   `json:"enable"`
	AdminPassword string `json:"adminPassword"`
}

// CpromInstanceStatus CProm实例状态
type CpromInstanceStatus struct {
	Phase          string                    `json:"phase"`
	Ready          bool                      `json:"ready"`
	Message        string                    `json:"message"`
	AccessEndpoint *CpromAccessEndpoint      `json:"accessEndpoint"`
}

// CpromAccessEndpoint 访问端点
type CpromAccessEndpoint struct {
	PrivateDomain string `json:"privateDomain"`
	PublicDomain  string `json:"publicDomain"`
}

// CpromInstanceDetailResponse CProm实例详情响应
type CpromInstanceDetailResponse struct {
	Success bool        `json:"success"`
	Result  *CPromItem  `json:"result"`
	Status  int         `json:"status"`
}

// PrometheusQueryRequest Prometheus查询请求参数
type PrometheusQueryRequest struct {
	InstanceId string `json:"instanceId" validate:"required"` // AI网关实例ID
	MetricType string `json:"metricType" validate:"required"` // 指标类型：basic/business
	Query      string `json:"query" validate:"required"`      // PromQL查询语句
	Step       string `json:"step" validate:"required"`       // 查询步长
	Start      string `json:"start" validate:"required"`      // 开始时间
	End        string `json:"end" validate:"required"`        // 结束时间
}
