package meta

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// AigwBls AIGW BLS日志任务模型
type AigwBls struct {
	dbutil.BaseModel
	InstanceUUID string `gorm:"column:instance_uuid" json:"instanceUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	LogStoreName string `gorm:"column:log_store_name" json:"logStoreName" dbutil:"searchable:wildcard,orderable" valid:"required"`
	TaskID       string `gorm:"column:task_id" json:"taskId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	AccountID    string `gorm:"column:account_id;default:0" json:"accountId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	ClusterUUID  string `gorm:"column:cluster_uuid;default:0" json:"clusterUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	Deleted      *int   `gorm:"column:deleted;default:0" json:"deleted" dbutil:"searchable:wildcard,orderable"`
}

// TableName 指定表名
func (bls *AigwBls) TableName() string {
	return "t_ai_gateway_bls_group"
}

// AigwBlsLogStoreName AIGW BLS日志集名称请求结构
type AigwBlsLogStoreName struct {
	Name string `json:"name" valid:"required"`
}

type BlsTemplateData struct {
	InstanceUUID string
	ClusterID    string
	Namespace    string
	LogStore     string
	AccountID    string
	PodNameKey   string
}
