# 验证CPROM实例token有效性

## 接口描述

验证当前用户使用的CPROM实例token是否有效。该接口会检查AI网关实例的监控状态，获取当前使用的token，并验证其是否在CPROM实例的有效token列表中。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/{instanceId}/metrics/token/validate
```

### 请求方法
GET

### 请求参数

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | String | 是 | AI网关实例ID |

#### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | bj |

## 响应信息

### 响应体结构

```json
{
  "success": true,
  "result": {
    "valid": true,
    "currentToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "status": 200
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| result | Object | 响应结果 |
| result.valid | Boolean | token是否有效 |
| result.currentToken | String | 当前使用的token值（仅在valid为true时返回） |
| status | Integer | HTTP状态码 |

## 业务逻辑

### 实现步骤

1. **输入参数验证**：验证网关实例ID参数
2. **获取实例信息**：通过网关实例ID获取AI网关实例的详细信息
3. **检查监控状态**：调用现有的监控状态检查逻辑，确认是否开启了业务指标监控
4. **获取配置信息**：如果已开启监控，从configMap配置文件中获取：
   - 用户当前使用的CPROM实例ID
   - 对应的bearer_token值
5. **获取有效token列表**：调用`core.MonitorService.GetInstanceTokenList`方法获取该CPROM实例的所有有效token列表
6. **验证token**：判断当前使用的token是否在有效token列表中
7. **返回结果**：
   - 如果token存在于列表中：返回`valid: true`和当前使用的token值
   - 如果token不存在于列表中：返回`valid: false`
   - 如果监控未开启：返回`valid: false`

### 错误处理

- **实例不存在**：返回404错误
- **监控未开启**：返回`valid: false`
- **配置获取失败**：返回相应的服务异常
- **CPROM服务调用失败**：返回相应的服务异常

## 请求示例

### 请求

```bash
curl -X GET \
  'https://csm.bj.baidubce.com/api/aigw/v1/aigateway/aigw-12345678/metrics/token/validate' \
  -H 'X-Region: bj'
```

### 响应示例

#### token有效

```json
{
  "success": true,
  "result": {
    "valid": true,
    "currentToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
  },
  "status": 200
}
```

#### token无效

```json
{
  "success": true,
  "result": {
    "valid": false
  },
  "status": 200
}
```

#### 监控未开启

```json
{
  "success": true,
  "result": {
    "valid": false
  },
  "status": 200
}
```

## 使用场景

### 1. 监控配置验证
在用户配置或修改监控设置后，可以通过此接口验证当前token是否仍然有效。

### 2. 故障排查
当监控数据采集出现问题时，可以通过此接口检查token有效性，快速定位问题。

### 3. 定期检查
可以定期调用此接口，确保监控配置的token始终有效。

### 4. 自动化运维
在自动化脚本中使用此接口，实现监控配置的自动验证和修复。

## 注意事项

### 1. 权限要求
- 需要对托管集群的 `monitoring` 命名空间有读权限
- 需要对 ConfigMap 资源的读取权限
- 需要调用CPROM服务API的权限

### 2. 性能考虑
- 接口会调用外部CPROM服务，响应时间可能较长
- 建议不要频繁调用，避免对CPROM服务造成压力

### 3. 安全性
- token信息敏感，仅在验证成功时返回
- 日志中不会记录完整的token值

### 4. 依赖服务
- 依赖CPROM服务的正常运行
- 依赖Kubernetes集群的正常访问
- 依赖ConfigMap配置的正确性

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 参数错误 | 检查instanceId参数是否正确 |
| 404 | 实例不存在 | 确认AI网关实例ID是否正确 |
| 500 | 服务内部错误 | 检查服务日志，联系技术支持 |

## 相关接口

- [检查监控状态](./检查监控状态.md) - 检查是否开启了业务指标监控
- [开启业务指标监控](./开启业务指标监控.md) - 为实例开启监控功能
- [关闭业务指标监控](./关闭业务指标监控.md) - 关闭实例的监控功能
