# 创建网关实例时开启业务指标监控

## 功能描述

在创建AI网关实例时，支持同时开启业务指标监控功能。用户只需在创建请求中传入 `cpromInstanceId` 参数，系统将在网关实例创建成功后自动配置业务指标监控。

## 实现原理

1. **参数检测**：系统检测创建请求中是否包含 `cpromInstanceId` 字段
2. **实例创建**：首先完成AI网关实例的创建流程
3. **监控配置**：在异步任务中自动调用业务指标监控开启逻辑
4. **容错处理**：监控配置失败不会影响网关实例的创建成功

## 请求参数扩展

### 新增字段

在原有的AI网关创建请求中，新增以下字段：

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
|--------|------|----------|------|--------|
| cpromInstanceId | String | 否 | CProm实例ID，如果传入则表示开启业务指标监控 | cprom-x35s7xaq7ezx7 |

### 完整请求示例

```json
{
  "name": "my-ai-gateway",
  "vpcId": "vpc-12345678",
  "vpcCidr": "***********/16",
  "subnetId": "subnet-12345678",
  "gatewayType": "medium",
  "isInternal": "false",
  "replicas": 2,
  "description": "AI网关实例",
  "deleteProtection": false,
  "clusters": [
    {
      "clusterId": "cce-12345678",
      "region": "bj"
    }
  ],
  "cpromInstanceId": "cprom-x35s7xaq7ezx7"
}
```

## 功能特性

### 1. **自动化配置**
- 无需手动调用开启监控接口
- 网关创建和监控配置一步完成
- 减少用户操作步骤

### 2. **容错机制**
- 监控配置失败不影响网关实例创建
- 详细的错误日志记录
- 用户可后续手动开启监控

### 3. **状态检查**
- 自动检查是否已开启监控，避免重复配置
- 支持CProm实例token的自动获取和创建
- 智能的Prometheus Agent配置管理

## 执行流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant API as API接口
    participant Core as 核心服务
    participant Monitor as 监控服务
    participant K8s as Kubernetes

    User->>API: 创建网关实例(包含cpromInstanceId)
    API->>Core: 调用CreateAIGateway
    Core->>Core: 创建网关实例
    
    alt cpromInstanceId不为空
        Core->>Core: 启动异步任务
        Core->>Monitor: 获取CProm实例token
        Monitor-->>Core: 返回token
        Core->>K8s: 配置Prometheus Agent
        Core->>K8s: 重启Prometheus Agent
        Core->>Core: 记录监控配置结果
    end
    
    Core-->>API: 返回创建结果
    API-->>User: 返回响应
```

## 日志记录

系统会记录详细的日志信息，便于问题排查：

### 成功日志示例
```
[INFO] 检测到cpromInstanceId参数，开始为实例aigw-12345678开启业务指标监控
[INFO] 开始为实例aigw-12345678开启业务指标监控，cpromInstanceId=cprom-x35s7xaq7ezx7
[INFO] 开始为用户配置Prometheus Agent的ConfigMap，region=bj, namespace=aigw-ns-12345678, clusterId=cce-12345678
[INFO] 成功为实例aigw-12345678开启业务指标监控
```

### 错误日志示例
```
[ERROR] 为实例aigw-12345678开启业务指标监控失败: CProm instance not found
[ERROR] 获取cprom实例cprom-x35s7xaq7ezx7的token列表失败: access denied
[ERROR] 配置Prometheus remote write失败: kubernetes api error
```

## 使用建议

### 1. **CProm实例准备**
- 确保CProm实例已创建且状态为Running
- 确保CProm实例与网关实例在同一区域
- 验证CProm实例的访问权限

### 2. **网络连通性**
- 确保网关所在集群能访问CProm实例
- 检查安全组和网络策略配置
- 验证域名解析是否正常

### 3. **监控验证**
- 创建完成后可通过监控状态检查接口验证
- 查看Prometheus Agent的配置是否正确
- 确认指标数据是否正常推送

## 相关接口

- [获取CProm实例列表](./获取CProm实例列表.md) - 获取可用的CProm实例
- [获取CProm实例详情](./获取CProm实例详情.md) - 查看CProm实例详细信息
- [检查监控状态](./检查监控状态.md) - 验证监控是否开启成功
- [开启业务指标监控](./开启业务指标监控.md) - 手动开启监控功能
- [关闭业务指标监控](./关闭业务指标监控.md) - 关闭监控功能

## 注意事项

1. **参数可选性**：`cpromInstanceId` 为可选参数，不传入则不开启监控
2. **异步执行**：监控配置在异步任务中执行，不会阻塞网关创建流程
3. **错误处理**：监控配置失败不会导致网关创建失败
4. **重复配置**：系统会自动检查避免重复配置监控
5. **权限要求**：需要确保用户对指定的CProm实例有访问权限
6. **区域匹配**：建议CProm实例与网关实例在同一区域以获得最佳性能

## 故障排查

### 常见问题

1. **CProm实例不存在**
   - 检查实例ID是否正确
   - 确认实例是否已被删除
   - 验证区域参数是否正确

2. **权限不足**
   - 检查用户是否有CProm实例的访问权限
   - 确认IAM策略配置是否正确

3. **网络连通性问题**
   - 检查集群网络配置
   - 验证安全组规则
   - 确认DNS解析是否正常

4. **Kubernetes配置失败**
   - 检查集群状态是否正常
   - 确认命名空间是否存在
   - 验证RBAC权限配置

### 排查步骤

1. 查看创建日志中的错误信息
2. 使用监控状态检查接口验证当前状态
3. 检查CProm实例的详细信息和状态
4. 验证网络连通性和权限配置
5. 如需要，可手动调用开启监控接口重试
