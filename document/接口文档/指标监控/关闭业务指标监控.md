# 关闭业务指标监控

## 接口描述

关闭AI网关实例的业务指标监控功能，删除Prometheus Agent配置中对应的remote_write配置，停止向CProm实例推送指标数据。

## 请求信息

### 请求路径
```
POST /api/aigw/v1/aigateway/{instanceId}/metrics/disable
```

### 请求方法
POST

### 请求参数

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | String | 是 | AI网关实例ID |

#### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | bj |

## 响应信息

### 响应体结构

```json
{
  "success": true,
  "result": {
    "instanceId": "aigw-12345678",
    "message": "业务指标监控已成功关闭"
  },
  "status": 200
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| result | Object | 响应结果 |
| result.instanceId | String | AI网关实例ID |
| result.message | String | 操作结果消息 |
| status | Integer | HTTP状态码 |

## 请求示例

### 请求

```bash
curl -X POST \
  'https://csm.bj.baidubce.com/api/aigw/v1/aigateway/aigw-12345678/metrics/disable' \
  -H 'X-Region: bj'
```

### 响应示例

#### 成功关闭监控

```json
{
  "success": true,
  "result": {
    "instanceId": "aigw-12345678",
    "message": "业务指标监控已成功关闭"
  },
  "status": 200
}
```

#### 监控未开启（重复操作）

```json
{
  "success": true,
  "result": {
    "instanceId": "aigw-12345678",
    "message": "业务指标监控未开启，无需关闭"
  },
  "status": 200
}
```

## 错误码

| 错误码 | 错误信息 | 描述 |
|--------|----------|------|
| 400 | InvalidParameterValue | 参数值无效 |
| 404 | ResourceNotFound | AI网关实例不存在 |
| 500 | InternalError | 内部服务错误 |
| 503 | ServiceUnavailable | 服务暂时不可用 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": "ResourceNotFound",
    "message": "AI网关实例不存在"
  },
  "status": 404
}
```

## 功能说明

### 操作流程

1. **参数验证**：验证AI网关实例ID的有效性
2. **状态检查**：检查当前监控状态，确认是否需要关闭
3. **配置删除**：从Kubernetes ConfigMap中删除对应的remote_write配置
4. **服务重启**：重启Prometheus Agent使配置生效

### 删除内容

关闭监控后，系统会从Prometheus Agent的配置中删除以下内容：

- **远程写入配置**：删除指向CProm实例的写入配置
- **认证信息**：清除相关的token认证配置
- **标签过滤规则**：移除用户命名空间的过滤规则
- **相关元数据**：清理所有相关的配置元数据

### 安全机制

- **精确匹配**：只删除指定用户命名空间的配置，不影响其他用户
- **配置验证**：删除前验证配置的存在性和正确性
- **回滚保护**：操作失败时自动回滚配置更改

## 注意事项

1. 该操作具有幂等性，重复调用不会产生副作用
2. 配置生效需要等待Prometheus Agent重启，通常需要30-60秒
3. 关闭监控后，将停止向CProm实例推送指标数据
4. 已推送到CProm实例的历史数据不会被删除
5. 需要有足够的权限操作目标集群的监控组件
6. 建议在关闭前确认不再需要监控数据

## 相关接口

- [检查监控状态](./检查监控状态.md) - 查看当前监控状态
- [开启业务指标监控](./开启业务指标监控.md) - 重新开启监控功能
- [获取CProm实例列表](./获取CProm实例列表.md) - 查看可用的CProm实例
