# 获取CProm实例列表

## 接口描述

获取当前用户可用的CProm（Cloud Prometheus）实例列表，用于配置AI网关的业务指标监控。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/cprom/instances
```

### 请求方法
GET

### 请求参数

#### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | bj |

## 响应信息

### 响应体结构

```json
{
  "success": true,
  "result": {
    "instances": [
      {
        "instanceId": "cprom-x35s7xaq7ezx7",
        "instanceName": "my-prometheus-instance",
        "region": "bj",
        "status": "Running",
        "createTime": "2024-01-15T10:30:00Z",
        "endpoint": "https://cprom.bj.baidubce.com"
      }
    ],
    "totalCount": 1
  },
  "status": 200
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| result | Object | 响应结果 |
| result.instances | Array | CProm实例列表 |
| result.instances[].instanceId | String | CProm实例ID |
| result.instances[].instanceName | String | CProm实例名称 |
| result.instances[].region | String | 实例所在区域 |
| result.instances[].status | String | 实例状态（Running/Stopped/Creating等） |
| result.instances[].createTime | String | 实例创建时间（ISO 8601格式） |
| result.instances[].endpoint | String | CProm实例访问端点 |
| result.totalCount | Integer | 实例总数 |
| status | Integer | HTTP状态码 |

## 请求示例

### 请求

```bash
curl -X GET \
  'https://csm.bj.baidubce.com/api/aigw/v1/aigateway/cprom/instances' \
  -H 'X-Region: bj'
```

### 响应示例

#### 有可用实例

```json
{
  "success": true,
  "result": {
    "instances": [
      {
        "instanceId": "cprom-x35s7xaq7ezx7",
        "instanceName": "production-prometheus",
        "region": "bj",
        "status": "Running",
        "createTime": "2024-01-15T10:30:00Z",
        "endpoint": "https://cprom.bj.baidubce.com"
      },
      {
        "instanceId": "cprom-y46t8ybr8fay8",
        "instanceName": "development-prometheus",
        "region": "gz",
        "status": "Running",
        "createTime": "2024-01-20T14:20:00Z",
        "endpoint": "https://cprom.gz.baidubce.com"
      }
    ],
    "totalCount": 2
  },
  "status": 200
}
```

#### 无可用实例

```json
{
  "success": true,
  "result": {
    "instances": [],
    "totalCount": 0
  },
  "status": 200
}
```

## 错误码

| 错误码 | 错误信息 | 描述 |
|--------|----------|------|
| 401 | Unauthorized | 身份认证失败 |
| 403 | Forbidden | 没有访问权限 |
| 500 | InternalError | 内部服务错误 |
| 503 | ServiceUnavailable | 服务暂时不可用 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": "Unauthorized",
    "message": "身份认证失败"
  },
  "status": 401
}
```

## 功能说明

### 实例状态说明

| 状态 | 描述 |
|------|------|
| Creating | 实例创建中 |
| Running | 实例运行中，可正常使用 |
| Stopped | 实例已停止 |
| Deleting | 实例删除中 |
| Error | 实例异常 |

### 使用场景

1. **配置监控前**：查看可用的CProm实例，选择合适的实例进行监控配置
2. **实例管理**：了解当前拥有的CProm实例状态和信息
3. **区域选择**：根据AI网关实例所在区域选择合适的CProm实例

### 筛选建议

- **区域匹配**：建议选择与AI网关实例相同区域的CProm实例，以降低网络延迟
- **状态检查**：只选择状态为"Running"的实例进行监控配置
- **容量考虑**：确保选择的CProm实例有足够的存储和处理能力

## 注意事项

1. 该接口只返回当前用户有权限访问的CProm实例
2. 实例列表会实时反映当前状态，建议在配置监控前重新获取
3. 不同区域的CProm实例可能有不同的功能特性和限制
4. 实例的endpoint用于数据推送，请确保网络连通性
5. 建议定期检查实例状态，避免使用异常实例

## 相关接口

- [开启业务指标监控](./开启业务指标监控.md) - 使用CProm实例配置监控
- [检查监控状态](./检查监控状态.md) - 查看当前使用的CProm实例
- [关闭业务指标监控](./关闭业务指标监控.md) - 停止使用CProm实例
