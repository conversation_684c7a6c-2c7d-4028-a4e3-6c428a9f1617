# 检查监控状态

## 接口描述

检查AI网关实例的业务指标监控状态，返回是否开启监控以及对应的CProm实例ID。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/{instanceId}/metrics/status
```

### 请求方法
GET

### 请求参数

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | String | 是 | AI网关实例ID |

#### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | bj |

## 响应信息

### 响应体结构

```json
{
  "success": true,
  "result": {
    "enabled": true,
    "cpromInstanceId": "cprom-x35s7xaq7ezx7"
  },
  "status": 200
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| result | Object | 响应结果 |
| result.enabled | Boolean | 是否开启业务指标监控 |
| result.cpromInstanceId | String | CProm实例ID（仅在enabled为true时返回） |
| status | Integer | HTTP状态码 |

## 请求示例

### 请求

```bash
curl -X GET \
  'https://csm.bj.baidubce.com/api/aigw/v1/aigateway/aigw-12345678/metrics/status' \
  -H 'X-Region: bj'
```

### 响应示例

#### 监控已开启

```json
{
  "success": true,
  "result": {
    "enabled": true,
    "cpromInstanceId": "cprom-x35s7xaq7ezx7"
  },
  "status": 200
}
```

#### 监控未开启

```json
{
  "success": true,
  "result": {
    "enabled": false
  },
  "status": 200
}
```

## 错误码

| 错误码 | 错误信息 | 描述 |
|--------|----------|------|
| 400 | InvalidParameterValue | 参数值无效 |
| 404 | ResourceNotFound | AI网关实例不存在 |
| 500 | InternalError | 内部服务错误 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": "ResourceNotFound",
    "message": "AI网关实例不存在"
  },
  "status": 404
}
```

## 注意事项

1. 该接口为查询接口，不会修改任何配置
2. 当监控未开启时，响应中不包含 `cpromInstanceId` 字段
3. 需要确保有足够的权限访问目标AI网关实例
4. 接口响应时间通常在1-3秒内
