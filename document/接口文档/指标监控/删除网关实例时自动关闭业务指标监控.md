# 删除网关实例时自动关闭业务指标监控

## 功能描述

在删除AI网关实例时，系统会自动检测并关闭该实例的业务指标监控功能。这确保了在实例删除后，不会留下无用的监控配置，避免资源浪费和配置冗余。

## 实现原理

1. **自动检测**：在删除网关实例时，系统自动检查是否开启了业务指标监控
2. **异步关闭**：在异步任务中执行监控关闭操作，不阻塞删除流程
3. **配置清理**：删除Prometheus Agent中对应的remote_write配置
4. **服务重启**：重启Prometheus Agent使配置变更生效
5. **容错处理**：监控关闭失败不会影响网关实例的删除成功

## 执行流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant API as API接口
    participant AIIngress as AIIngress服务
    participant K8s as Kubernetes
    participant Prometheus as Prometheus Agent

    User->>API: 删除网关实例
    API->>AIIngress: 调用DeleteAIGateway
    AIIngress->>AIIngress: 检查删除保护和依赖
    
    par 异步关闭监控
        AIIngress->>K8s: 检查监控配置
        K8s-->>AIIngress: 返回ConfigMap
        AIIngress->>AIIngress: 解析配置检查是否开启监控
        
        alt 监控已开启
            AIIngress->>K8s: 删除remote_write配置
            AIIngress->>K8s: 更新ConfigMap
            AIIngress->>K8s: 重启StatefulSet
            K8s->>Prometheus: 重启Pod
            Prometheus->>Prometheus: 加载新配置
        else 监控未开启
            AIIngress->>AIIngress: 跳过关闭操作
        end
    and 删除网关资源
        AIIngress->>AIIngress: 删除数据库记录
        AIIngress->>K8s: 删除Higress资源
        AIIngress->>AIIngress: 清理网络资源
    end
    
    AIIngress-->>API: 返回删除结果
    API-->>User: 返回响应
```

## 核心实现

### 1. **删除流程集成**

在 `pkg/service/aiingress/service.go` 的 `DeleteAIGateway` 方法中集成了监控关闭逻辑：

```go
// 卸载Higress
go func() {
    // 首先尝试关闭业务指标监控
    ctx.CsmLogger().Infof("开始为删除的实例%s关闭业务指标监控", instanceId)
    err := s.disableMetricsForDeletedInstance(ctx, instanceId, region, gatewayNamespace, hostedClusterId)
    if err != nil {
        ctx.CsmLogger().Errorf("为删除的实例%s关闭业务指标监控失败: %v", instanceId, err)
        // 监控关闭失败不阻断删除流程
    } else {
        ctx.CsmLogger().Infof("成功为删除的实例%s关闭业务指标监控", instanceId)
    }

    // 继续执行Higress卸载逻辑...
}()
```

### 2. **监控关闭主方法**

`disableMetricsForDeletedInstance` 方法实现了完整的监控关闭逻辑：

```go
func (s *Service) disableMetricsForDeletedInstance(ctx csmContext.CsmContext, instanceId, region, userNamespace, hostedClusterId string) error {
    // 参数验证
    if hostedClusterId == "" {
        ctx.CsmLogger().Warnf("实例%s的托管集群ID为空，跳过关闭监控", instanceId)
        return nil
    }

    // 创建k8s客户端
    hostingClient, err := s.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
    if err != nil {
        return err
    }

    // 检查监控状态
    status, err := s.getMetricsStatusForDelete(ctx, hostingClient, userNamespace)
    if err != nil || !status.Enabled {
        return err
    }

    // 删除remote_write配置
    err = s.removePrometheusRemoteWriteForDelete(ctx, hostingClient, userNamespace)
    if err != nil {
        return err
    }

    // 重启prometheus agent
    return s.restartPrometheusAgentForDelete(ctx, hostingClient)
}
```

### 3. **配置管理方法**

实现了一系列辅助方法来处理Kubernetes配置：

- **`getMetricsStatusForDelete`**: 检查监控是否已开启
- **`removePrometheusRemoteWriteForDelete`**: 删除remote_write配置
- **`removeRemoteWriteConfigForDelete`**: 解析并删除YAML配置
- **`isTargetRemoteWriteEntryForDelete`**: 精确定位目标配置条目
- **`restartPrometheusAgentForDelete`**: 重启StatefulSet

## 功能特性

### 1. **智能化设计**
- **自动检测**：只对已开启监控的实例执行关闭操作
- **异步执行**：不阻塞网关删除流程
- **容错处理**：监控关闭失败不影响网关删除成功

### 2. **精确配置管理**
- **YAML解析**：逐行解析prometheus.yaml配置
- **精确删除**：只删除目标实例的配置，保留其他实例配置
- **格式保持**：维护YAML文件的完整性和格式

### 3. **完整的错误处理**
- **参数验证**：检查托管集群ID有效性
- **权限检查**：处理Kubernetes API访问权限问题
- **详细日志**：记录每个步骤的执行状态和错误信息

## 日志记录

系统会记录详细的日志信息，便于问题排查：

### 成功日志示例
```
[INFO] 开始为删除的实例aigw-12345678关闭业务指标监控
[INFO] 开始关闭删除实例aigw-12345678的监控，region=bj, namespace=aigw-ns-12345678, clusterId=cce-12345678
[INFO] 成功删除用户命名空间aigw-ns-12345678的remote_write配置
[INFO] 成功重启Prometheus Agent StatefulSet
[INFO] 成功为删除的实例aigw-12345678关闭业务指标监控
```

### 跳过日志示例
```
[WARN] 实例aigw-12345678的托管集群ID为空，跳过关闭监控
[INFO] 实例aigw-12345678的业务指标监控未开启，无需关闭
```

### 错误日志示例
```
[ERROR] 为删除的实例aigw-12345678关闭业务指标监控失败: failed to get configmap
[ERROR] 创建k8s客户端失败: cluster not found
[ERROR] 删除Prometheus remote write配置失败: configmap update failed
```

## 配置清理机制

### 1. **YAML解析**
- 逐行解析prometheus.yaml配置文件
- 识别remote_write配置块
- 定位目标用户命名空间的配置

### 2. **精确删除**
- 查找包含目标用户命名空间的remote_write条目
- 删除整个配置块（包括所有子配置）
- 保持YAML文件格式的完整性

### 3. **配置验证**
- 检查regex配置中的用户命名空间匹配
- 确保删除的是正确的配置条目
- 避免误删其他实例的配置

## 使用场景

### 1. **正常删除流程**
- 用户通过控制台或API删除网关实例
- 系统自动检查并关闭监控
- 清理所有相关配置和资源

### 2. **批量删除**
- 删除多个网关实例时
- 每个实例的监控都会被自动关闭
- 避免配置文件中的冗余条目

### 3. **故障恢复**
- 实例异常时需要删除重建
- 自动清理旧实例的监控配置
- 为新实例的监控配置做准备

## 注意事项

### 1. **异步执行**
- 监控关闭是异步执行的
- 删除操作会立即返回成功
- 监控关闭可能需要额外时间完成

### 2. **权限要求**
- 需要对托管集群的monitoring命名空间有访问权限
- 需要对ConfigMap和StatefulSet资源的操作权限
- 确保服务账号有足够的RBAC权限

### 3. **配置生效时间**
- Prometheus Agent重启后配置才会生效
- 可能需要等待30-60秒
- 在此期间可能仍会推送少量数据

### 4. **错误处理**
- 监控关闭失败不会阻止网关删除
- 可以通过日志查看具体的失败原因
- 必要时可以手动清理残留配置

## 相关接口

- [开启业务指标监控](./开启业务指标监控.md) - 手动开启监控功能
- [关闭业务指标监控](./关闭业务指标监控.md) - 手动关闭监控功能
- [检查监控状态](./检查监控状态.md) - 查看监控状态
- [创建网关实例时开启业务指标监控](./创建网关实例时开启业务指标监控.md) - 创建时开启监控

## 故障排查

### 常见问题

1. **托管集群访问失败**
   - 检查集群状态是否正常
   - 确认网络连通性
   - 验证访问权限配置

2. **ConfigMap操作失败**
   - 检查RBAC权限配置
   - 确认monitoring命名空间存在
   - 验证ConfigMap是否存在

3. **StatefulSet重启失败**
   - 检查StatefulSet状态
   - 确认Pod资源是否充足
   - 查看Pod重启日志

### 排查步骤

1. 查看删除操作的日志记录
2. 检查托管集群的连通性和权限
3. 验证monitoring命名空间中的资源状态
4. 确认Prometheus Agent的运行状态
5. 如有必要，手动清理残留的监控配置
