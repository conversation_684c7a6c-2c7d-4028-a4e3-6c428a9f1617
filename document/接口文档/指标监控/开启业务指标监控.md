# 开启业务指标监控

## 接口描述

为AI网关实例开启业务指标监控功能，配置Prometheus Agent的remote_write，将业务指标数据推送到指定的CProm实例。

## 请求信息

### 请求路径
```
POST /api/aigw/v1/aigateway/{instanceId}/metrics/{cpromInstanceId}/enable
```

### 请求方法
POST

### 请求参数

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | String | 是 | AI网关实例ID |
| cpromInstanceId | String | 是 | CProm实例ID |

#### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | bj |

## 响应信息

### 响应体结构

```json
{
  "success": true,
  "result": {
    "instanceId": "aigw-12345678",
    "cpromInstanceId": "cprom-x35s7xaq7ezx7",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "message": "业务指标监控已成功开启"
  },
  "status": 200
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| result | Object | 响应结果 |
| result.instanceId | String | AI网关实例ID |
| result.cpromInstanceId | String | CProm实例ID |
| result.token | String | CProm访问token |
| result.message | String | 操作结果消息 |
| status | Integer | HTTP状态码 |

## 请求示例

### 请求

```bash
curl -X POST \
  'https://csm.bj.baidubce.com/api/aigw/v1/aigateway/aigw-12345678/metrics/cprom-x35s7xaq7ezx7/enable' \
  -H 'X-Region: bj'
```

### 响应示例

#### 成功开启监控

```json
{
  "success": true,
  "result": {
    "instanceId": "aigw-12345678",
    "cpromInstanceId": "cprom-x35s7xaq7ezx7",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
    "message": "业务指标监控已成功开启"
  },
  "status": 200
}
```

#### 监控已开启（重复操作）

```json
{
  "success": true,
  "result": {
    "instanceId": "aigw-12345678",
    "cpromInstanceId": "cprom-x35s7xaq7ezx7",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
    "message": "业务指标监控已开启，无需重复配置"
  },
  "status": 200
}
```

## 错误码

| 错误码 | 错误信息 | 描述 |
|--------|----------|------|
| 400 | InvalidParameterValue | 参数值无效 |
| 404 | ResourceNotFound | AI网关实例或CProm实例不存在 |
| 500 | InternalError | 内部服务错误 |
| 503 | ServiceUnavailable | 服务暂时不可用 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": "ResourceNotFound",
    "message": "AI网关实例不存在"
  },
  "status": 404
}
```

## 功能说明

### 操作流程

1. **参数验证**：验证AI网关实例ID和CProm实例ID的有效性
2. **Token管理**：获取或创建CProm实例的访问token
3. **状态检查**：检查是否已经开启监控，避免重复配置
4. **配置更新**：在Kubernetes ConfigMap中添加remote_write配置
5. **服务重启**：重启Prometheus Agent使配置生效

### 配置内容

开启监控后，系统会在Prometheus Agent的配置中添加以下内容：

- **远程写入URL**：指向CProm实例的写入端点
- **认证信息**：使用生成的token进行身份认证
- **标签过滤**：只推送指定用户命名空间的指标数据
- **区域标签**：自动添加区域标识标签

## 注意事项

1. 该操作具有幂等性，重复调用不会产生副作用
2. 配置生效需要等待Prometheus Agent重启，通常需要30-60秒
3. 开启监控后，指标数据会持续推送到CProm实例
4. 确保CProm实例有足够的存储空间和处理能力
5. 需要有足够的权限操作目标集群的监控组件
