# 网关实例开启BLS日志采集任务

## 接口基本信息

- **接口说明**：为指定的网关实例开启BLS日志采集任务
- **接口地址**：`/api/aigw/v1/aigateway/{InstanceId}/bls/logTask` 
- **请求方式**：POST
- **数据格式**：JSON

### 路径参数:

| 字段       | 类型   | 是否必需 | 默认值 | 备注       |
| ---------- | ------ | -------- | ------ | ---------- |
| InstanceId | string | 是       |        | 网关实例ID |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 请求体参数:

| 名称 | 类型   | 是否必须 | 默认值 | 备注       | 其他信息 |
| ---- | ------ | -------- | ------ | ---------- | -------- |
| name | string | 必须     |        | 日志集名称 |          |

**请求示例**:
```json
{
  "name": "aigw-r2uqhxu0-logs"
}
```

### 响应参数:

| 名称    | 类型    | 是否必须 | 默认值 | 备注     | 其他信息 |
| ------- | ------- | -------- | ------ | -------- | -------- |
| success | boolean | 必须     |        | 是否成功 |          |
| status  | string  | 必须     |        | 状态     |          |
| result  | boolean | 必须     |        | 结果     |          |

**响应示例**:
```json
{
  "success": true,
  "status": "success",
  "result": true
}
```

## 业务流程

### 用户开启日志服务流程

1. **用户选择开启日志服务**
    - 用户在网关实例界面选择开启日志服务
    - 用户选择要接收日志的数据集（如: aigw-r2uqhxu0-logs）

2. **创建传输任务的Log Config CR**
    - 系统自动采集 higress-gateway 日志
    - 生成对应的 LogConfig CR 配置

3. **LogConfig CR 示例**:
```yaml
apiVersion: cce.baidubce.com/v1
kind: LogConfig
metadata:
  name: aigw-r2uqhxu0-logconfig           # 日志配置名字
  namespace: default                      # 日志配置所在 namespace
spec:
  srcConfig: # 定义日志来源 
    srcType: container                    # 日志类型为容器
    logType: stdout                       # 容器 stdout 输出日志
    matchedPattern: "^.*$"
    srcDir: "/var/log"
    ttl: 3                                
    matchLabels:                          # 这里的 label 对应 docker inspect 上的 label
      - key: io.kubernetes.pod.name
        value: higress-gateway-.*               # gateway pod name
      - key: io.kubernetes.pod.namespace
        value: istio-system-aigw-r2uqhxu0       # 具体 namespace name
  dstConfig:
    dstType: BLS                          # 日志输出目的端类型，当前只支持 BLS
    logStore: aigw-r2uqhxu0-logs          # 用户选择的日志集名称
    accountID: eca97e148cb74e9683d7b7240829d1ff  # 用户的ID
    retention: 10                         # BLS logstore 中日志保存时间
    rateLimit: 10                         # 日志上传带宽限制 1-99 MB/s
```

## 错误码

| 错误码 | 错误信息                | 描述                     |
| ------ | ----------------------- | ------------------------ |
| 400    | Invalid parameter       | 参数错误                 |
| 404    | Instance not found      | 网关实例不存在           |
| 500    | Internal server error   | 服务器内部错误           |
| 503    | Service unavailable     | BLS服务不可用            |

## 注意事项

1. 所有接口都需要进行IAM签名验证
2. 需要在请求头中设置正确的Region信息
3. 开启和关闭BLS日志采集任务需要相应的权限
4. 日志采集任务的状态变更可能需要一定时间生效
5. 建议在调用接口前先检查网关实例状态
6. 同一个网关实例只能有一个活跃的日志采集任务
7. 删除任务时会进行软删除，不会物理删除数据库记录
