#  网关实例获取BLS采集任务

## 接口基本信息

- **接口说明**：获取指定网关实例的BLS日志采集任务状态和详情
- **接口地址**：`/api/aigw/v1/aigateway/{InstanceId}/bls/log` 
- **请求方式**：GET
- **数据格式**：JSON

### 路径参数:

| 字段       | 类型   | 是否必需 | 默认值 | 备注       |
| ---------- | ------ | -------- | ------ | ---------- |
| InstanceId | string | 是       |        | 网关实例ID |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 响应参数:

| 名称            | 类型      | 是否必须 | 默认值 | 备注                      | 其他信息                    |
|---------------| --------- | -------- | ------ | ------------------------- | --------------------------- |
| - result      | object[]  | 必须     |        | BLS任务列表               | item 类型: object           |
| -- name       | string    | 必须     |        | 日志集名称                |                             |
| -- createTime | string    | 必须     |        | 创建时间                  |                             |
| -- retention  | string    | 必须     |        | 日志保留天数              |                             |
| -- updateTime | string    | 必须     |        | 更新时间                  |                             |
| status        | string    | 必须     |        | 状态(Open/Close/Abnormal) |                             |

**状态说明**:
- `Close`: 数据库无数据或者都已删除
- `Open`: 数据库数据与BLS侧数据一致，返回对应的blsStorage详情
- `Abnormal`: 数据库与BLS数据不一致，或者task任务异常

**响应示例**:
```json
{
  "result": [
    {
      "name": "k8s-audit-cce-ezxpzzxb",
      "retention": 180,
      "createTime": "2025-07-03T11:02:56Z",
      "updateTime": "2025-07-03T11:02:57Z"
    }
  ],
  "status": "Open"
}
```
