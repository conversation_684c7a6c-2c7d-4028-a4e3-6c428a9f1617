# 获取BLS日志集列表

## 接口基本信息

**接口描述**: 获取指定区域的所有BLS日志集列表

**接口地址**: `GET /api/aigw/v1/bls/logStoreList`

### 请求参数: 无

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 响应参数:

| 名称       | 类型     | 是否必须 | 默认值 | 备注                   | 其他信息                   |
| ---------- | -------- | -------- | ------ | ---------------------- | -------------------------- |
| - result   | object[] | 非必须   |        | 日志集列表             | item 类型: object          |
| -- name    | string   | 必须     |        | 日志集名称             |                            |
| -- retention | integer | 必须     |        | 日志保留天数           |                            |
| -- region  | string   | 必须     |        | 区域                   |                            |
| pageNo     | integer  | 非必须   |        | 页码                   |                            |
| pageSize   | integer  | 非必须   |        | 每页大小               |                            |
| totalCount | integer  | 非必须   |        | 总数                   |                            |

**响应示例**:
```json
{
   "result": [
      {
         "name": "cce-rp0yquzg-cce-virtual-kubelet-log",
         "retention": 10,
         "region": "bj"
      },
      {
         "name": "k8s-audit-cce-ezxpzzxb",
         "retention": 180,
         "region": "bj"
      },
      {
         "name": "k8s-audit-cce-acg7as9h",
         "retention": 180,
         "region": "bj"
      }
   ],
   "pageNo": 1,
   "pageSize": 3,
   "totalCount": 3
}
```
